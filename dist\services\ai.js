/**
 * AI Service for handling chat completions across multiple providers
 */
import { ProviderFactory } from '../providers/index.js';
import { ConfigService } from './config.js';
export class AIService {
    configService;
    constructor() {
        this.configService = ConfigService.getInstance();
    }
    /**
     * Send a chat completion request
     */
    async chat(messages, options = {}) {
        const config = this.buildConfig(options);
        try {
            ProviderFactory.validateConfig(config);
            const client = ProviderFactory.getClient(config);
            const openaiMessages = messages.map(msg => ({
                role: msg.role,
                content: msg.content,
            }));
            const completionParams = {
                model: config.model,
                messages: openaiMessages,
            };
            if (config.temperature !== undefined) {
                completionParams.temperature = config.temperature;
            }
            if (config.maxTokens !== undefined) {
                completionParams.max_tokens = config.maxTokens;
            }
            const completion = await client.chat.completions.create(completionParams);
            const choice = completion.choices[0];
            if (!choice?.message?.content) {
                throw new Error('No response content received from AI provider');
            }
            return {
                content: choice.message.content,
                usage: completion.usage ? {
                    promptTokens: completion.usage.prompt_tokens,
                    completionTokens: completion.usage.completion_tokens,
                    totalTokens: completion.usage.total_tokens,
                } : undefined,
                model: completion.model,
                provider: config.provider,
            };
        }
        catch (error) {
            throw this.handleError(error, config.provider);
        }
    }
    /**
     * Send a single message and get response
     */
    async sendMessage(message, systemPrompt, options = {}) {
        const messages = [];
        if (systemPrompt) {
            messages.push({
                role: 'system',
                content: systemPrompt,
            });
        }
        messages.push({
            role: 'user',
            content: message,
        });
        return this.chat(messages, options);
    }
    /**
     * Test connection to the AI provider
     */
    async testConnection(options = {}) {
        const config = this.buildConfig(options);
        try {
            return await ProviderFactory.testConnection(config);
        }
        catch (error) {
            throw this.handleError(error, config.provider);
        }
    }
    /**
     * Build complete configuration from options and defaults
     */
    buildConfig(options) {
        const globalConfig = this.configService.getConfig();
        const provider = options.provider || globalConfig.defaultProvider;
        const model = options.model || globalConfig.defaultModel;
        // Get provider-specific config if it exists
        const providerConfig = this.configService.getProviderConfig(provider);
        // Get API key
        const apiKey = options.apiKey ||
            this.configService.getApiKey(provider) ||
            providerConfig?.apiKey;
        if (!apiKey) {
            throw new Error(`No API key found for provider ${provider}. ` +
                `Please set the environment variable or configure it.`);
        }
        const config = {
            provider,
            model,
            apiKey,
            temperature: options.temperature ?? providerConfig?.temperature ?? globalConfig.temperature,
            maxTokens: options.maxTokens ?? providerConfig?.maxTokens ?? globalConfig.maxTokens,
        };
        if (options.baseURL || providerConfig?.baseURL) {
            config.baseURL = options.baseURL || providerConfig?.baseURL;
        }
        return config;
    }
    /**
     * Handle and format errors from AI providers
     */
    handleError(error, provider) {
        const providerError = new Error();
        providerError.provider = provider;
        if (error instanceof Error) {
            // Enhanced error message handling
            let enhancedMessage = error.message;
            // Handle specific error types with actionable suggestions
            if (error.message.includes('Connection error') || error.message.includes('ENOTFOUND')) {
                enhancedMessage = `Connection error: Cannot reach ${provider} API. Please check your internet connection and try again.`;
            }
            else if (error.message.includes('timeout') || error.message.includes('AbortError')) {
                enhancedMessage = `Request timeout: The ${provider} API took too long to respond. Please try again.`;
            }
            else if (error.message.includes('401') || error.message.includes('Unauthorized')) {
                enhancedMessage = `Authentication error: Invalid API key for ${provider}. Please check your API key configuration.`;
            }
            else if (error.message.includes('403') || error.message.includes('Forbidden')) {
                enhancedMessage = `Access denied: Your API key doesn't have permission to access this ${provider} resource.`;
            }
            else if (error.message.includes('429') || error.message.includes('rate limit')) {
                enhancedMessage = `Rate limit exceeded: Too many requests to ${provider}. Please wait a moment and try again.`;
            }
            else if (error.message.includes('500') || error.message.includes('502') || error.message.includes('503')) {
                enhancedMessage = `Server error: ${provider} API is experiencing issues. Please try again later.`;
            }
            else if (error.message.includes('ECONNREFUSED')) {
                enhancedMessage = `Connection refused: Cannot connect to ${provider} API. Please check your internet connection.`;
            }
            providerError.message = enhancedMessage;
            providerError.name = error.name;
            if (error.stack) {
                providerError.stack = error.stack;
            }
            // Handle OpenAI-specific errors
            if ('status' in error) {
                providerError.statusCode = error.status;
            }
            if ('error' in error) {
                providerError.details = error.error;
            }
        }
        else {
            providerError.message = `Unknown error occurred with provider ${provider}`;
            providerError.details = error;
        }
        return providerError;
    }
}
//# sourceMappingURL=ai.js.map