/**
 * AI Provider configurations and factory
 */
import OpenAI from 'openai';
import { type AIProvider, type AIProviderConfig } from '../types/index.js';
export declare class ProviderFactory {
    private static instances;
    /**
     * Get or create an OpenAI client instance for the specified provider
     */
    static getClient(config: AIProviderConfig): OpenAI;
    /**
     * Get available providers
     */
    static getAvailableProviders(): AIProvider[];
    /**
     * Get provider by name
     */
    static getProvider(name: string): AIProvider | undefined;
    /**
     * Validate provider configuration
     */
    static validateConfig(config: AIProviderConfig): void;
    /**
     * Validate API key format for specific providers
     */
    static validateApiKey(provider: string, apiKey: string): void;
    /**
     * Test connection to provider API
     */
    static testConnection(config: AIProviderConfig): Promise<boolean>;
    /**
     * Clear cached instances (useful for testing)
     */
    static clearCache(): void;
}
/**
 * Default configurations for each provider
 */
export declare const DEFAULT_PROVIDER_CONFIGS: Record<string, Partial<AIProviderConfig>>;
/**
 * Get default configuration for a provider
 */
export declare function getDefaultConfig(providerName: string): Partial<AIProviderConfig>;
//# sourceMappingURL=index.d.ts.map