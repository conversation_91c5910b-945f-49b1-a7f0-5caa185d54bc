/**
 * Modern Message Input Component
 * A sophisticated input component with enhanced styling and features for the chat interface
 */

import chalk from 'chalk';
import inquirer from 'inquirer';
import type { MessageInputConfig, MessageInputResult, InputHistoryItem } from '../../types/index.js';

export class MessageInput {
  private config: Required<MessageInputConfig>;
  private history: InputHistoryItem[];
  private static instance: MessageInput | null = null;

  constructor(config: MessageInputConfig = {}) {
    this.config = {
      placeholder: config.placeholder || 'Type your message...',
      multiline: config.multiline ?? false, // Keep simple for now
      maxLines: config.maxLines || 5,
      showCharCount: config.showCharCount ?? false,
      showWordCount: config.showWordCount ?? false,
      enableHistory: config.enableHistory ?? true,
      enableAutoComplete: config.enableAutoComplete ?? false, // Keep simple for now
      theme: {
        borderColor: 'cyan',
        focusColor: 'blue',
        textColor: 'white',
        placeholderColor: 'gray',
        counterColor: 'yellow',
        errorColor: 'red',
        successColor: 'green',
        ...config.theme,
      },
    };

    this.history = [];
  }

  /**
   * Get singleton instance
   */
  static getInstance(config?: MessageInputConfig): MessageInput {
    if (!MessageInput.instance) {
      MessageInput.instance = new MessageInput(config);
    }
    return MessageInput.instance;
  }

  /**
   * Display the modern input prompt and wait for user input
   */
  async prompt(message: string = 'You:'): Promise<MessageInputResult> {
    // Display the modern input header
    this.displayInputHeader(message);

    try {
      const { userInput } = await inquirer.prompt([
        {
          type: 'input',
          name: 'userInput',
          message: this.createStyledPrompt(),
          validate: (input: string) => {
            const trimmed = input.trim();
            if (trimmed.length === 0) {
              return chalk.hex(this.config.theme.errorColor!)('Please enter a message');
            }
            return true;
          },
          transformer: (input: string, { isFinal }: { isFinal: boolean }) => {
            if (!isFinal && input.length === 0) {
              return chalk.hex(this.config.theme.placeholderColor!)(this.config.placeholder);
            }
            return chalk.hex(this.config.theme.textColor!)(input);
          },
        },
      ]);

      const trimmedInput = userInput.trim();

      // Handle special commands
      if (trimmedInput.toLowerCase() === 'exit') {
        this.displayInputFooter();
        return { value: trimmedInput, command: 'exit' };
      }

      if (trimmedInput.toLowerCase() === 'clear') {
        this.displayInputFooter();
        return { value: trimmedInput, command: 'clear' };
      }

      // Add to history
      if (this.config.enableHistory) {
        this.addToHistory(trimmedInput);
      }

      this.displayInputFooter();
      return { value: trimmedInput };

    } catch (error) {
      this.displayInputFooter();
      return { value: '', cancelled: true };
    }
  }

  /**
   * Display the modern input header with styling
   */
  private displayInputHeader(message: string): void {
    const theme = this.config.theme;

    // Create a modern header with box styling
    const headerLine = chalk.hex(theme.borderColor!)('┌─') +
                      chalk.hex(theme.focusColor!)(`─ ${message} `) +
                      chalk.hex(theme.borderColor!)('─'.repeat(Math.max(0, 50 - message.length - 4)) + '┐');

    console.log(headerLine);

    // Add some helpful hints
    const hintLine = chalk.hex(theme.borderColor!)('│ ') +
                    chalk.hex(theme.placeholderColor!)('💡 Type "exit" to quit, "clear" to clear history') +
                    chalk.hex(theme.borderColor!)(' '.repeat(Math.max(0, 48 - 45)) + '│');

    console.log(hintLine);

    // Add separator
    const separatorLine = chalk.hex(theme.borderColor!)('├' + '─'.repeat(58) + '┤');
    console.log(separatorLine);
  }

  /**
   * Create styled prompt message
   */
  private createStyledPrompt(): string {
    const theme = this.config.theme;
    return chalk.hex(theme.borderColor!)('│ ') + chalk.hex(theme.focusColor!)('✨');
  }

  /**
   * Display the input footer
   */
  private displayInputFooter(): void {
    const theme = this.config.theme;
    const footerLine = chalk.hex(theme.borderColor!)('└' + '─'.repeat(58) + '┘');
    console.log(footerLine);
  }

  /**
   * Display input statistics (character/word count)
   */
  private displayInputStats(value: string): void {
    if (!this.config.showCharCount && !this.config.showWordCount) return;

    const theme = this.config.theme;
    let stats = '';

    if (this.config.showCharCount) {
      stats += `${value.length} chars`;
    }

    if (this.config.showWordCount) {
      const wordCount = value.trim().split(/\s+/).filter(word => word.length > 0).length;
      stats += stats ? ` • ${wordCount} words` : `${wordCount} words`;
    }

    if (stats) {
      const statsLine = chalk.hex(theme.borderColor!)('│ ') +
                       chalk.hex(theme.counterColor!)(`📊 ${stats}`) +
                       chalk.hex(theme.borderColor!)(' '.repeat(Math.max(0, 54 - stats.length)) + '│');
      console.log(statsLine);
    }
  }

  /**
   * Add input to history
   */
  private addToHistory(value: string): void {
    // Don't add empty values or duplicates
    if (value.trim().length === 0) return;

    const lastItem = this.history[this.history.length - 1];
    if (lastItem && lastItem.value === value) return;

    this.history.push({
      value,
      timestamp: new Date(),
    });

    // Keep history size manageable (last 100 items)
    if (this.history.length > 100) {
      this.history = this.history.slice(-100);
    }
  }

  /**
   * Show error message with modern styling
   */
  showError(message: string): void {
    const theme = this.config.theme;
    const errorLine = chalk.hex(theme.borderColor!)('│ ') +
                     chalk.hex(theme.errorColor!)(`❌ ${message}`) +
                     chalk.hex(theme.borderColor!)(' '.repeat(Math.max(0, 54 - message.length)) + '│');
    console.log(errorLine);
  }

  /**
   * Show success message with modern styling
   */
  showSuccess(message: string): void {
    const theme = this.config.theme;
    const successLine = chalk.hex(theme.borderColor!)('│ ') +
                       chalk.hex(theme.successColor!)(`✅ ${message}`) +
                       chalk.hex(theme.borderColor!)(' '.repeat(Math.max(0, 54 - message.length)) + '│');
    console.log(successLine);
  }

  /**
   * Get input history
   */
  getHistory(): InputHistoryItem[] {
    return [...this.history];
  }

  /**
   * Clear input history
   */
  clearHistory(): void {
    this.history = [];
  }

  /**
   * Set custom theme
   */
  setTheme(theme: Partial<MessageInputConfig['theme']>): void {
    this.config.theme = { ...this.config.theme, ...theme };
  }

  /**
   * Get the last N history items
   */
  getRecentHistory(count: number = 10): InputHistoryItem[] {
    return this.history.slice(-count);
  }

  /**
   * Check if history is enabled
   */
  isHistoryEnabled(): boolean {
    return this.config.enableHistory;
  }

  /**
   * Get current configuration
   */
  getConfig(): Required<MessageInputConfig> {
    return { ...this.config };
  }
}
